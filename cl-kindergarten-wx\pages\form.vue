<template>
	<view class="content">
		<u-form :model="form" ref="uForm">
			<u-form-item label="标识号:" prop="login_code"  label-width="170">
				<u-input placeholder="请输入标识号,若不知,请联系所属公司" v-model="form.login_code" />
			</u-form-item>
			<u-form-item label="真实姓名:" label-width="170" prop="name">
				<u-input placeholder="请输入真实姓名" v-model="form.name" />
			</u-form-item>
			<u-form-item label="手机号:"  label-width="170" prop="phone">
				<u-input placeholder="请输入输入手机号码" v-model="form.phone" />
			</u-form-item>
			<u-form-item label="登陆密码:"  label-width="170" prop="password">
				<u-input placeholder="请输入登陆密码" v-model="form.password" />
			</u-form-item>
			<u-form-item label="身份证号:" label-width="170" prop="cardno">
				<u-input placeholder="请输入身份证号码" v-model="form.cardno" />
			</u-form-item>
			<u-form-item label="身份证正反面:" label-width="190">
				<u-upload upload-text="选择或拍照" width="180" height="180" ref="uUpload" :action="action"  :fileList="fileList" :form-data="form_data" ></u-upload> 
			</u-form-item>
			
			<view class="agreement">
				<u-checkbox size="40rpx" v-model="check" @change="checkboxChange"></u-checkbox>
				<view class="agreement-text">
					我已知晓并同意 <text @click="showPopup(4)" style="color:cornflowerblue">《用户服务协议》</text>及<text @click="showPopup(5)" style="color:cornflowerblue">《隐私政策》</text>
				</view>
			</view>
			
			<view class="submit_con">
				<u-button type="primary" @click="submit">提交注册</u-button>
			</view>
		</u-form>
		
		<u-popup v-model="popupVisible" mode="bottom" length="60%">
			  <view class="popup-content">
				<rich-text :nodes="protocolsContent" style="line-height: 50rpx;"></rich-text>
			  </view>
			  <view class="popup-footer"> 
			
				<u-button class="close_pop" size="medium" type="primary" @click="hidePopup">关闭</u-button>
			  </view>
		</u-popup>
	</view>
</template>

<script>
	import {toast, clearStorageSync, setStorageSync,getStorageSync, useRouter} from '@/utils/utils.js'
	export default {
		data() {
			return {
				action: this.$api_url + '/api/index/upload_cos',
				fileList:[],
				popupVisible: false,
				protocolsContent: "",
				check: false,
				agreement: false,
				form: {
					login_code: '',
					name: '',
					phone: '',
					cardno: '',
					photo:'',
					password:''
				},
				form_data : {
					floder : 'photo'
				},
				rules: {
					login_code: [
						{ 
							required: true, 
							type:"number",
							message: '请输入标识号', 
							// 可以单个或者同时写两个触发验证方式 
							trigger: ['change','blur'],
						}
					],
					password: [
						{ 
							required: true, 
							message: '请输入密码', 
							// 可以单个或者同时写两个触发验证方式 
							trigger: ['change','blur'],
						}
					],
					name: [
						{ 
							required: true, 
							message: '请输入真实姓名', 
							// 可以单个或者同时写两个触发验证方式 
							trigger: ['change','blur'],
						}
					],
					phone: [
						{ 
							required: true, 
							type:"number",
							message: '请输入登陆手机号', 
							// 可以单个或者同时写两个触发验证方式 
							trigger: ['change','blur'],
						}
					],
					cardno: [
						{ 
							required: true, 
							message: '请输入身份证号码', 
							// 可以单个或者同时写两个触发验证方式 
							trigger: ['change','blur'],
						}
					]

				}
			};
		},
		onReady(){
			this.$refs.uForm.setRules(this.rules);
		},
		
		methods: {
			checkboxChange(e) {
				this.agreement = e.value;
			},
			showPopup(id) {
			    this.popupVisible = true;
				const params ={id:id}
				this.$api.page(params).then(res => {
					this.protocolsContent = res.data.content
				})	
			},
			 hidePopup() {
			    this.popupVisible = false;
			},
			submit() {
				this.$refs.uForm.validate(valid => {
					if (valid) {
						if(!this.agreement) return toast('请查看并同意相关协议');
						//获取上传列表
						let _uploadPhoto_data = {}
						var _list = this.$refs.uUpload.lists
									
						for (let i = 0; i < _list.length; i++) {
							 _uploadPhoto_data[i] = {
							 	url: _list[i].response.data.file
							 }
						} 
						
						if(Object.keys(_uploadPhoto_data).length ==0 ){
							toast('必须上传身份证正反面')
							return
						}
					
						this.form.photo = JSON.stringify(_uploadPhoto_data)
						this.$api.register(this.form).then(res => {
							if(res.code==0){
								toast(res.msg)
								setTimeout(()=>{
									 uni.navigateBack()
								},1500)
								
								console.log(res); 
							}else{
								toast(res.msg)
								console.log(res);
							}
						})	
					} else {
						console.log('验证失败'); 
					}
				})
			}
		},
	}
</script>

<style lang="scss">
	
	.popup-content {
	  padding: 40rpx;
	  padding-bottom: 120rpx;
	}
	
	.popup-footer {
	  margin-top: 20rpx;
	  text-align: center;
	  position: fixed;
	  bottom: 0;
	  width: 100%;
	  
	}
	
	
.content{padding:30rpx}
.agreement {
	display: flex;
	margin: 40rpx 0 0 10rpx;

	.agreement-text {
		padding-left: 0rpx;
		color: $u-tips-color;
	}
}
.submit_con{margin-top: 20rpx;}
</style>

