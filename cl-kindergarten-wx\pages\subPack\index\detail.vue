<template>
	<view class="content">
	    <view class="banner" auto-focus>
	      <image class="banner-img" :src="detail.thumb"></image>
	      <view class="title-area">
	        <text class="title-text">{{detail.title}}</text>
	      </view>
	    </view> 
	    <view class="article-meta">
	      <text class="article-meta-text article-author">{{detail.source}}</text>
	      <text class="article-meta-text article-text">发表于</text>
	      <text class="article-meta-text article-time">{{detail.addtime}}</text>
	    </view>
	    <view class="article-content">
			
	
			<view v-html="html"></view>
			
	      
	    </view>
	    <view class="comment-wrap"></view>
	  </view>
</template>
<script>
	export default {
			data() {
				return {
					detail: [],
					 html: ''
					
				}
			},
			onLoad(op) {
				this.id  = op.id
				this.getInfo()
				
			},
			onReady() {
				
				
			},
			methods: {
				getInfo(){
					const params = {
						id : this.id
					}
					this.$api.detail(params).then(res => {
						if (res.code == 0) {
							this.detail = res.data
							 this.html = res.data.content
							 console.log( this.html)
						}else{
							
						}
					})
				},
			}
		}
	</script> 
<style lang="scss" scoped>	

	  page {
	    min-height: 100%;
	  }
	

	  .banner {
	    height: 380rpx;
	    position: relative;
	    background-color: #ccc;
	    flex-direction: row;
	    overflow: hidden;
	  }
	
	  .banner-img {
	    width:100%
	  }
	
	  .title-area {
	    position: absolute;
	    left: 15rpx;
	    right: 15rpx;
	    bottom: 15rpx;
	    z-index: 11;
	  }
	
	  .title-text {
	    font-size: 40rpx;
	    font-weight: 400;
	    line-height: 20rpx;
	    lines: 2;
	    color: #ffffff;
	    overflow: hidden;
	  }
	
	  .article-meta {
	    padding: 10rpx 15rpx;

		font-size: 30rpx;
	  }
	
	  .article-meta-text {
	    color: gray;
		font-size: 30rpx;
	  }
	
	  .article-text {
	    font-size: 32rpx;
	    line-height: 25rpx;
	    margin: 0 10rpx;
	  }
	
	  .article-author {
	    font-size: 32rpx;
	  }
	
	  .article-time {
	    font-size:30rpx;
	  }
	
	  .article-content {
	    font-size: 36rpx;
	    padding: 0 35rpx;
	    margin-bottom: 35rpx;
	    overflow: hidden;
		color: #333;
	  }
	
	
	
	  .article-content img {
	    max-width: 100%;
	  }
	  .article-content p{line-height:40rpx; margin-top: 40rpx;}

</style>