<template>
	<view class="content">
		<u-form :model="form" ref="uForm" :error-type="['toast']">
				<u-form-item label="登陆帐号:" prop="username"  label-width="170"><u-input placeholder="请输入登陆帐号" v-model="form.username" /></u-form-item>
				<u-form-item label="真实姓名:" label-width="170" prop="name"><u-input placeholder="请输入真实姓名" v-model="form.name" /></u-form-item>
				<u-form-item label="手机号:"  label-width="170" prop="phone"><u-input placeholder="请输入输入手机号码" v-model="form.phone" /></u-form-item>
				<u-form-item label="登陆密码:"  label-width="170" prop="password"><u-input type="password" placeholder="请输入登陆密码" v-model="form.password" /></u-form-item>
				
				<view class="agreement">
					<u-checkbox size="40rpx" v-model="check" @change="checkboxChange"></u-checkbox>
					<view class="agreement-text">
						我已知晓并同意 <text @click="showPopup(3)" style="color:cornflowerblue">《用户服务协议》</text>及<text @click="showPopup(4)" style="color:cornflowerblue">《隐私政策》</text>
					</view>
				</view>
				
				
				<view class="submit_con">
					<u-button type="primary" @click="submit">提交注册</u-button>
				</view>
			</u-form>
			
			
			<u-popup v-model="popupVisible" mode="bottom" length="60%" :closeable="true">
			      <scroll-view class="popup-content" :scroll-y="true">
			        <rich-text :nodes="protocolsContent" style="line-height: 50rpx;"></rich-text>
			      </scroll-view>
			</u-popup>
				
				
	</view>
</template>

<script>
	import {toast, clearStorageSync, setStorageSync,getStorageSync, useRouter} from '@/utils/utils.js'
	export default {
		data() {
			return {
				action: this.$api_url + '/api/index/upload_cos',
				fileList:[],
				popupVisible: false,
				protocolsContent: "",
				check: false,
				agreement: false,
				form: {
					username: '',
					name: '',
					phone: '',
					cardno: '',
					photo:'',
					password:''
				},
				form_data : {
					floder : 'photo'
				},
				rules: {
					username: [
						{ 
							required: true, 
							message: '请输入登陆帐号', 
							// 可以单个或者同时写两个触发验证方式 
							trigger: ['change','blur'],
						}
					],
					password: [
						{ 
							required: true, 
							message: '请输入密码', 
							// 可以单个或者同时写两个触发验证方式 
							trigger: ['change','blur'],
						}
					],
					name: [
						{ 
							required: true, 
							message: '请输入真实姓名', 
							// 可以单个或者同时写两个触发验证方式 
							trigger: ['change','blur'],
						}
					],
					phone: [
						{ 
							required: true, 
							type:"number",
							message: '请输入登陆手机号', 
							// 可以单个或者同时写两个触发验证方式 
							trigger: ['change','blur'],
						}
					],
				}
			};
		},
		onReady(){
			this.$refs.uForm.setRules(this.rules);
		},
		
		methods: {
			checkboxChange(e) {
				this.agreement = e.value;
			},
			showPopup(id) {
			    this.popupVisible = true;
				
				const params ={id:id}
				this.$api.page(params).then(res => {
					//console.log('list', res.data);
					this.protocolsContent = res.data.content
				})	

			},
			 hidePopup() {
			    this.popupVisible = false;
			},
			
			submit() {
				this.$refs.uForm.validate(valid => {
					if (valid) {
						if(!this.agreement) return toast('请查看并同意相关协议');
						this.$api.register(this.form).then(res => {
							if(res.code==0){
								toast(res.msg)
								setTimeout(()=>{
									 uni.navigateBack()
								},1500)
								
								console.log(res); 
							}else{
								toast(res.msg)
								console.log(res);
							}
						})	
						
						
						console.log(this.form)
					} else {
						
						console.log('验证失败'); 
					}
				})
				
			}
		},
	}
</script>

<style lang="scss">
	.popup-content {
	  padding: 40rpx;
	  margin-top: 60rpx;
	  margin-bottom: 20rpx;
	  width: 90%;
	  height:100%
	}

.content{padding:30rpx}
.agreement {
	display: flex;
	margin: 40rpx 0 0 10rpx;

	.agreement-text {
		padding-left: 0rpx;
		color: $u-tips-color;
	}
}
.submit_con{margin-top: 20rpx;}
</style>

