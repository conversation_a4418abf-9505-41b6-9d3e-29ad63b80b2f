<template>
	<view class="content">
		<view class="logo">
			<u-image src="@/static/logo.png" width="150rpx" height="150rpx" mode="aspectFill"></u-image>
		</view>
		
		<u-cell-item icon="info-circle-fill" bg-color="#fff" title="当前版本" hover-class="none" :value="version" :arrow="false"></u-cell-item>
		
		<u-cell-group title="其他:">
			<u-cell-item icon="home-fill" title="公司简介" @click="about"></u-cell-item>
			<u-cell-item icon="integral-fill" title="公司资质" @click="Qualification"></u-cell-item>
			<u-cell-item icon="file-text-fill" title="服务协议" @click="serviceAgreement" ></u-cell-item>
			<u-cell-item icon="file-text-fill" title="隐私政策" @click="privacyPolicy"></u-cell-item>
		</u-cell-group>
		
		<u-cell-group title="联系:">
			<u-cell-item icon="server-fill" title="联系我们" @click="contactUs"></u-cell-item>
			<u-cell-item icon="chat-fill" title="意见反馈" @click="feedback"></u-cell-item>
		</u-cell-group>
	</view>
</template>
<script>
import {toast, clearStorageSync, setStorageSync, Jump,getStorageSync, useRouter} from '@/utils/utils.js'
export default {
	data() {
		return {
			version:"",
		}
	},
	onLoad(op) {
		//this.id  = op.id
	},
	onShow() {
	},
	onReady() {
		const that = this
		uni.getSystemInfo({
			success: function (res) {
				console.log("res", res)
				that.version = res.appVersion
			}
		})
	},
	methods: {
		about(){
			Jump('/pages/public/page?id=1')
		},
		Qualification(){
			Jump('/pages/public/page?id=2')
		},
		serviceAgreement(){
			Jump('/pages/public/page?id=3')
		},
		privacyPolicy(){
			Jump('/pages/public/page?id=4')
		},
		contactUs(){
			Jump('/pages/public/page?id=5')
		},
		feedback(){
			useRouter('/pages/my/about/feedback',{} ,'navigateTo')
		},
	}
}
</script>
<style lang="scss">	
    page{background:#f2f2f2}
	.logo{width: 150rpx;height: 150rpx; margin:0 auto;margin-top: 30rpx; margin-bottom: 30rpx;}
</style>
