<template>
	<view class="content">
		<view class="custom-card">
		    <view class="card-header">
			    <view class="header-left">
				    <u-tag :text="item.goods_type" type="success" shape="circleRight"/>
				    <view class="header-title"></view>
			    </view>
			    <view class="header-right">
					<u-tag text="待服务" shape="circleLeft" type="primary"  v-if="item.type==1"/>
					<u-tag text="服务中"  shape="circleLeft" type="info"  v-if="item.type==2"/>
					<u-tag text="待验收"  shape="circleLeft" type="warning"  v-if="item.type==3"/>
					<u-tag text="问题单"  shape="circleLeft" type="error"  v-if="item.type==4"/>
					<u-tag text="已完成"  shape="circleLeft" type="success"  v-if="item.type==5"/>
			    </view>
		    </view>
		    <view class="card-body" @click="detail(item.id)">
				<u-alert-tips type="warning"  :description="item.remark" v-if="item.remark"></u-alert-tips>
				
				<view class="tags">
					<u-tag text="加急" type="error" mode="dark" v-if="item.urgent==1"/>
					<u-tag text="设备在家" type="primary" mode="light" v-if="item.have_device==1"/>
					<u-tag text="设备不在家" type="primary" mode="light" v-if="item.have_device==0"/>
					<u-tag text="收费" type="warning" mode="light" v-if="item.charge==1"/>
					<u-tag text="不收费" type="warning" mode="light" v-if="item.charge==0"/>
					<u-tag text="签约单" type="success" mode="light" v-if="item.sign_order==1"/>
				</view>
				
				<view class="section-title">基本信息：</view>
				<view class="card-body-item">
					<view class="item-title">订单编号：</view>
					<view class="item-content">{{item.order_sn}}</view>
				</view>

				<view class="section-title">派单信息：</view>
				
				<view class="card-body-item">
					<view class="item-title">派单时间：</view>
					<view class="item-content">{{item.dispatch_time}}</view>
				</view>
				
				<view class="card-body-item">
					<view class="item-title">安装师傅：</view>
					<view class="item-content">{{item.master_name}}</view>
				</view>
				
				<view class="card-body-item">
					<view class="item-title">师傅电话：</view>
					<view class="item-content">{{item.master_tel}}</view>
				</view>
		    </view>
		</view>
	</view>
</template>
<script>
import {toast, clearStorageSync, setStorageSync, getStorageSync, useRouter} from '@/utils/utils.js'
export default {
	data() {
		return {
			item : []
		}
	},
	onLoad(op) {
		this.id  = op.id
		this.getDetail()
	},
	onShow() {
	},
	onReady() {
	},
	methods: {
		getDetail(){
			const data = {
				advance_id : this.id
			}
			this.$api.advanceDetail(data).then(res => {
				if (res.code == 0) {
					this.item = res.data
				} else {
					
				}
			})
		},
	}
}
</script>
<style lang="scss">
page{background: #f2f2f2;}
.content{padding: 20rpx;}
.custom-card {
		  background-color: #fff;
		  border: 1px solid #f2f2f2;
		  border-radius: 4px;
		  padding: 8px 0;
		  margin-top: 15rpx;
		  .card-header {
		    border-bottom: 1px solid #f2f2f2;
		    padding-bottom: 16rpx;
		    display: flex;
		    justify-content: space-between;
		    .header-left{
				display: flex;  
				.header-title{
					color:#333;
					font-weight: bold;
					margin-top: 3rpx;
					margin-left: 10rpx;
				}
		    }
		    .header-right{
				.date{font-size: 26rpx;color:#999;font-weight: normal;margin-right: 15rpx;margin-top: 5rpx;}
		    }
		  }
		  .card-body{
			padding: 20rpx;
			.section-title{font-weight: bold; margin-top: 20rpx;}
			.card-body-item{
				display: flex;
				line-height: 80rpx;
				border-bottom: 1px solid #efefef;
				.item-title{color:#A9A9A9}
				.item-content{
					flex:1;
					color:#666;
					.goods_list{display: flex;line-height: 60rpx;
						.install{width: 60%;}
						.price{width: 20%;}
						.master_money{width: 20%;}
					}
					.photo_list{ display: flex;}
					.photo_list image{width:80rpx;height:100rpx; margin-top: 10rpx; margin-left: 10rpx;}
				}
				.item-right{margin-right: 8rpx;}
			}
			.tags{
				margin-top: 10rpx;
				line-height:60rpx;
				.u-tag{margin-right: 15rpx;}
			}
			.fee{
				background: #F9F9F9;
				padding:20rpx;
				display: flex;
				.fee-item{width: 50%;text-align: center;
					.fee-item-title{
						color: #A9A9A9;
					}
				}
			}
		  }
		  .card-footer {
			display: flex;
		  }
		}
</style>
