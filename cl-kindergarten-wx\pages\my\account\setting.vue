<template>
	<view class="content">
		<view class="">
			<u-cell-group>
				<u-cell-item  title="通知"  :arrow="false">
					<u-switch  v-model="costs_display" @change="costs_display_change"></u-switch>
				</u-cell-item>
				
			</u-cell-group>	
		</view>
	</view>
</template>
<script>
import {toast, clearStorageSync, setStorageSync, getStorageSync, useRouter} from '@/utils/utils.js'
export default {
	data() {
		return {
			costs_display: false,
		}
	},
	onLoad(op) {
		this.costs_display = getStorageSync("costs_display") || false
	},
	onShow() {
	},
	onReady() {
	},
	methods: {
		costs_display_change(status){
			setStorageSync("costs_display",status)
			console.log('status' ,getStorageSync("costs_display"))
		}
	}
}
</script>
<style lang="scss" scoped>	
	.content{padding: 30rpx;}
</style>
