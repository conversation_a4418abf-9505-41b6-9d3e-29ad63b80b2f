<template>
	<view class="content">
		<mp-html :content="html" :tag-style="tag_style"/>
		<!-- <rich-text :nodes="html"></rich-text> -->
	</view>
</template>
<script>
import {toast, clearStorageSync, setStorageSync, getStorageSync, useRouter} from '@/utils/utils.js'
export default {
	data() {
		return {
			html: '',
			tag_style:{
				 p: 'line-height:45rpx; margin-top: 30rpx;',
				 img : 'width:100%',
			}
		}
	},
	onLoad(op) {
		this.id  = op.id
		this.getPage()
	},
	onShow() {
	},
	onReady() {
	},
	methods: {
		getPage(){
			const param = {
				id : this.id
			}
			this.$api.page(param).then(res => {
				if (res.code ==1 ) {
					uni.setNavigationBarTitle({
						title: res.data.title
					})
					this.html = res.data.content
					//解析HTML用到了插件mp-html,更多文档见：https://jin-yufeng.gitee.io/mp-html/#/overview/quickstart
				} 
			})
		}, 
	}
}
</script>
<style lang="scss">	
	.content{
		padding: 20rpx;
	}
</style>
