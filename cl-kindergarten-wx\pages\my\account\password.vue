<template>
	<view class="content">
		<u-form :model="form" ref="uForm" :error-type="['toast']">
			<u-form-item label="原密码:" labelWidth="120" prop="password">
				<u-input placeholder="请输入原密码" v-model="form.password" />
			</u-form-item>
			
			<u-form-item label="新密码:" labelWidth="120" prop="news_password">
				<u-input placeholder="请输入要修改的新密码" v-model="form.news_password" />
			</u-form-item>
			<u-button type="primary" @click="submit">确定</u-button>
		</u-form>
	</view>
	
</template>
<script>
import {toast, useRouter} from '@/utils/utils.js'	
export default {
	data() {
		return {
			form: {
				password: '',
				new_password :''
			},
			rules: {
				password: [
					{
						required: true,
						message: '请输入原密码',
						trigger: 'blur,change'
					}
				],
				news_password: [
					{
						required: true,
						message: '请输入新密码',
						trigger: 'blur,change'
					}
				]
			},
		};
	},
	onReady() {
		this.$refs.uForm.setRules(this.rules);
	},
	methods: {
		submit(){
			this.$refs.uForm.validate(valid => {
				if (valid) {
					//验证通过，执行TODO
					this.$api.password(this.form).then(res => {
						if (res.code == 0) {
							toast(res.msg)
							setTimeout(()=>{
								uni.navigateBack()
							},500) 
						} else {
							toast(res.msg)
						}
					}) 
					
				} else {
					console.log('验证失败');
				}
			});
		}
	}
};
</script>

<style lang="scss" scoped>
	.content{padding:35rpx}
</style>