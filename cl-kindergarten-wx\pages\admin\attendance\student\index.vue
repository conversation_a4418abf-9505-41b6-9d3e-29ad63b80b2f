<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="header">
			<view class="header-content">
				<view class="nav-left" @click="goBack">
					<u-icon name="arrow-left" color="#ffffff" size="20"></u-icon>
				</view>
				<view class="header-title">
					<text class="title-text">学生考勤管理</text>
				</view>
				<view class="nav-right">
					<u-icon name="calendar" color="#ffffff" size="20"></u-icon>
				</view>
			</view>
		</view>

		<!-- 日期和统计卡片 -->
		<view class="date-stats-card">
			<view class="date-section">
				<view class="date-nav">
					<view class="date-btn" @click="changeDate(-1)">
						<u-icon name="arrow-left" color="#667eea" size="16"></u-icon>
					</view>
					<view class="current-date">
						<text class="date-text">{{ currentDate }}</text>
					</view>
					<view class="date-btn" @click="changeDate(1)">
						<u-icon name="arrow-right" color="#667eea" size="16"></u-icon>
					</view>
				</view>
			</view>

			<view class="stats-section">
				<view class="stat-card total">
					<view class="stat-icon">👥</view>
					<view class="stat-info">
						<text class="stat-number">{{ totalStudents }}</text>
						<text class="stat-label">总人数</text>
					</view>
				</view>
				<view class="stat-card present">
					<view class="stat-icon">✅</view>
					<view class="stat-info">
						<text class="stat-number">{{ presentCount }}</text>
						<text class="stat-label">已到园</text>
					</view>
				</view>
				<view class="stat-card absent">
					<view class="stat-icon">❌</view>
					<view class="stat-info">
						<text class="stat-number">{{ absentCount }}</text>
						<text class="stat-label">未到园</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 班级筛选 -->
		<view class="filter-section">
			<view class="filter-title">班级筛选</view>
			<view class="filter-buttons">
				<view
					v-for="(classItem, index) in classFilter"
					:key="index"
					class="filter-btn"
					:class="{ active: selectedClass === classItem.value }"
					@click="filterByClass(classItem.value)"
				>
					<text class="filter-text">{{ classItem.label }}</text>
				</view>
			</view>
		</view>

		<!-- 考勤列表 -->
		<view class="attendance-list">
			<view v-for="classGroup in filteredAttendanceData" :key="classGroup.className" class="class-section">
				<view class="class-header">
					<view class="class-icon">🏫</view>
					<view class="class-info">
						<text class="class-name">{{ classGroup.className }}</text>
						<text class="class-count">{{ classGroup.students.length }}名学生</text>
					</view>
					<view class="class-progress">
						<view class="progress-bar">
							<view
								class="progress-fill"
								:style="{ width: getClassProgress(classGroup) + '%' }"
							></view>
						</view>
						<text class="progress-text">{{ getClassProgress(classGroup) }}%</text>
					</view>
				</view>

				<view class="students-grid">
					<view
						v-for="student in classGroup.students"
						:key="student.id"
						class="student-card"
						:class="{
							'selection-mode': isSelectionMode,
							'selected': selectedStudents.includes(student.id),
							'selectable': isSelectionMode && student.status === 'absent'
						}"
						@click="handleStudentClick(student)"
					>
						<view class="card-header">
							<view class="student-avatar" :class="student.status">
								<text class="avatar-text">{{ student.name.charAt(0) }}</text>
								<view class="status-indicator" :class="student.status"></view>
								<!-- 选择模式下的复选框 -->
								<view v-if="isSelectionMode && student.status === 'absent'" class="selection-checkbox" :class="{ checked: selectedStudents.includes(student.id) }">
									<u-icon v-if="selectedStudents.includes(student.id)" name="checkmark" color="#ffffff" size="12"></u-icon>
								</view>
							</view>
							<view class="student-basic">
								<text class="student-name">{{ student.name }}</text>
								<text class="student-no">{{ student.studentNo }}</text>
							</view>
							<view class="quick-actions">
								<view class="action-dot" @click="editAttendance(student)">
									<u-icon name="more-dot-fill" color="#cccccc" size="16"></u-icon>
								</view>
							</view>
						</view>

						<view class="card-content">
							<view class="time-info">
								<view class="time-item" v-if="student.checkInTime">
									<view class="time-icon checkin">🌅</view>
									<view class="time-details">
										<text class="time-label">入园时间</text>
										<text class="time-value">{{ student.checkInTime }}</text>
									</view>
								</view>
								<view class="time-item" v-if="student.checkOutTime">
									<view class="time-icon checkout">🌇</view>
									<view class="time-details">
										<text class="time-label">离园时间</text>
										<text class="time-value">{{ student.checkOutTime }}</text>
									</view>
								</view>
								<view class="time-item" v-if="!student.checkInTime">
									<view class="time-icon absent">⏰</view>
									<view class="time-details">
										<text class="time-label">状态</text>
										<text class="time-value absent">未到园</text>
									</view>
								</view>
							</view>

							<view class="status-badge" :class="student.status">
								<text class="status-text">{{ getStatusText(student.status) }}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 浮动操作按钮 -->
		<view class="floating-actions">
			<view class="fab-container">
				<view class="fab-btn" :class="{ active: isSelectionMode }" @click="toggleSelectionMode">
					<u-icon :name="isSelectionMode ? 'checkmark' : 'checkbox'" color="#ffffff" size="20"></u-icon>
					<text class="fab-text">{{ isSelectionMode ? `确认签到(${selectedStudents.length})` : '批量签到' }}</text>
				</view>
			</view>
		</view>

		<!-- 选择模式提示 -->
		<view v-if="isSelectionMode" class="selection-tip">
			<view class="tip-content">
				<u-icon name="info-circle" color="#667eea" size="16"></u-icon>
				<text class="tip-text">请选择需要签到的学生</text>
				<view class="tip-actions">
					<text class="cancel-selection" @click="cancelSelection">取消</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { toast, useRouter } from '@/utils/utils.js'

export default {
	data() {
		return {
			currentDate: '',
			selectedClass: 'all',
			isSelectionMode: false,
			selectedStudents: [],
			totalStudents: 75,
			presentCount: 68,
			absentCount: 7,
			classFilter: [
				{ label: '全部', value: 'all' },
				{ label: '小班', value: 'small' },
				{ label: '中班', value: 'medium' },
				{ label: '大班', value: 'large' }
			],
			attendanceData: [
				{
					className: '小班',
					classValue: 'small',
					students: [
						{
							id: 1,
							name: '张小明',
							studentNo: '2024001',
							checkInTime: '08:15',
							checkOutTime: '',
							status: 'present'
						},
						{
							id: 2,
							name: '李小红',
							studentNo: '2024002',
							checkInTime: '08:20',
							checkOutTime: '',
							status: 'present'
						},
						{
							id: 3,
							name: '王小华',
							studentNo: '2024003',
							checkInTime: '',
							checkOutTime: '',
							status: 'absent'
						}
					]
				},
				{
					className: '中班',
					classValue: 'medium',
					students: [
						{
							id: 4,
							name: '赵小美',
							studentNo: '2024025',
							checkInTime: '',
							checkOutTime: '',
							status: 'leave',
							leaveReason: '病假 - 发烧'
						}
					]
				},
				{
					className: '大班',
					classValue: 'large',
					students: [
						{
							id: 5,
							name: '孙小强',
							studentNo: '2024050',
							checkInTime: '07:45',
							checkOutTime: '',
							status: 'present'
						}
					]
				}
			]
		}
	},
	computed: {
		filteredAttendanceData() {
			if (this.selectedClass === 'all') {
				return this.attendanceData
			}
			return this.attendanceData.filter(classGroup => classGroup.classValue === this.selectedClass)
		}
	},
	onLoad() {
		this.initCurrentDate()
	},
	methods: {
		initCurrentDate() {
			const now = new Date()
			const year = now.getFullYear()
			const month = String(now.getMonth() + 1).padStart(2, '0')
			const day = String(now.getDate()).padStart(2, '0')
			const weekdays = ['日', '一', '二', '三', '四', '五', '六']
			const weekday = weekdays[now.getDay()]
			this.currentDate = `${year}年${month}月${day}日 周${weekday}`
		},
		
		goBack() {
			uni.navigateBack()
		},
		
		changeDate(direction) {
			toast(`切换到${direction > 0 ? '下' : '上'}一天`)
		},
		
		filterByClass(classValue) {
			this.selectedClass = classValue
		},
		
		getStatusText(status) {
			const statusMap = {
				present: '已到园',
				absent: '未到园',
				leave: '请假'
			}
			return statusMap[status] || '未知'
		},

		getClassProgress(classGroup) {
			const presentStudents = classGroup.students.filter(s => s.status === 'present').length
			return Math.round((presentStudents / classGroup.students.length) * 100)
		},
		
		editAttendance(student) {
			toast(`编辑 ${student.name} 的考勤记录`)
		},
		
		toggleSelectionMode() {
			if (this.isSelectionMode) {
				// 确认批量签到
				if (this.selectedStudents.length === 0) {
					toast('请选择需要签到的学生')
					return
				}
				this.batchCheckIn()
			} else {
				// 进入选择模式
				this.isSelectionMode = true
				this.selectedStudents = []
			}
		},

		cancelSelection() {
			this.isSelectionMode = false
			this.selectedStudents = []
		},

		handleStudentClick(student) {
			if (this.isSelectionMode) {
				if (student.status === 'absent') {
					// 切换选择状态
					const index = this.selectedStudents.indexOf(student.id)
					if (index > -1) {
						this.selectedStudents.splice(index, 1)
					} else {
						this.selectedStudents.push(student.id)
					}
				}
			} else {
				// 非选择模式，编辑考勤
				this.editAttendance(student)
			}
		},

		batchCheckIn() {
			if (this.selectedStudents.length === 0) {
				toast('请选择需要签到的学生')
				return
			}

			uni.showModal({
				title: '确认签到',
				content: `确定为选中的 ${this.selectedStudents.length} 名学生签到吗？`,
				success: (res) => {
					if (res.confirm) {
						// 执行批量签到逻辑
						console.log('批量签到学生ID:', this.selectedStudents)
						toast(`已为 ${this.selectedStudents.length} 名学生签到`)

						// 更新学生状态
						this.updateStudentsStatus()

						// 退出选择模式
						this.cancelSelection()
					}
				}
			})
		},

		updateStudentsStatus() {
			// 更新选中学生的状态为已到园
			const currentTime = new Date().toLocaleTimeString('zh-CN', {
				hour12: false,
				hour: '2-digit',
				minute: '2-digit'
			})

			this.attendanceData.forEach(classGroup => {
				classGroup.students.forEach(student => {
					if (this.selectedStudents.includes(student.id)) {
						student.status = 'present'
						student.checkInTime = currentTime
					}
				})
			})

			// 重新计算统计数据
			this.calculateStats()
		},

		calculateStats() {
			let total = 0
			let present = 0
			let absent = 0

			this.attendanceData.forEach(classGroup => {
				classGroup.students.forEach(student => {
					total++
					if (student.status === 'present') {
						present++
					} else if (student.status === 'absent') {
						absent++
					}
				})
			})

			this.totalStudents = total
			this.presentCount = present
			this.absentCount = absent
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 顶部导航栏 */
.header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 0 30rpx;
	padding-top: var(--status-bar-height, 44rpx);
	box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
}

.header-content {
	height: 120rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.nav-left, .nav-right {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.15);
	backdrop-filter: blur(10rpx);
	border: 1rpx solid rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

	&:active {
		transform: scale(0.95);
		background: rgba(255, 255, 255, 0.25);
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
	}
}

.header-title {
	flex: 1;
	text-align: center;
}

.title-text {
	display: block;
	font-size: 36rpx;
	font-weight: 700;
	color: #ffffff;
	margin-bottom: 8rpx;
}

.subtitle-text {
	display: block;
	font-size: 24rpx;
	color: rgba(255, 255, 255, 0.8);
	font-weight: 400;
}

/* 日期统计卡片 */
.date-stats-card {
	margin: 30rpx;
	background: #ffffff;
	border-radius: 24rpx;
	padding: 40rpx;
	box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.1);
	border: 1rpx solid rgba(255, 255, 255, 0.8);
}

.date-section {
	margin-bottom: 40rpx;
}

.date-nav {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 40rpx;
}

.date-btn {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	background: rgba(102, 126, 234, 0.1);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.9);
		background: rgba(102, 126, 234, 0.2);
	}
}

.current-date {
	text-align: center;
}

.date-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
}

.stats-section {
	display: flex;
	gap: 20rpx;
}

.stat-card {
	flex: 1;
	padding: 30rpx 20rpx;
	border-radius: 20rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 16rpx;
	position: relative;
	overflow: hidden;

	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 6rpx;
	}

	&.total {
		background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
		&::before { background: linear-gradient(90deg, #2196f3 0%, #1976d2 100%); }
	}

	&.present {
		background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
		&::before { background: linear-gradient(90deg, #4caf50 0%, #388e3c 100%); }
	}

	&.absent {
		background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
		&::before { background: linear-gradient(90deg, #f44336 0%, #d32f2f 100%); }
	}
}

.stat-icon {
	font-size: 32rpx;
}

.stat-info {
	text-align: center;
}

.stat-number {
	display: block;
	font-size: 36rpx;
	font-weight: 700;
	color: #333333;
	margin-bottom: 8rpx;
}

.stat-label {
	display: block;
	font-size: 24rpx;
	color: #666666;
	font-weight: 500;
}

/* 筛选区域 */
.filter-section {
	margin: 0 30rpx 30rpx;
	background: #ffffff;
	border-radius: 20rpx;
	padding: 30rpx;
	box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
}

.filter-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 24rpx;
}

.filter-buttons {
	display: flex;
	gap: 16rpx;
	flex-wrap: wrap;
}

.filter-btn {
	padding: 16rpx 32rpx;
	border-radius: 50rpx;
	background: #f8f9fa;
	border: 2rpx solid transparent;
	transition: all 0.3s ease;

	&.active {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		border-color: #667eea;
		transform: translateY(-2rpx);
		box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);
	}
}

.filter-text {
	font-size: 26rpx;
	font-weight: 500;
	color: #666666;

	.filter-btn.active & {
		color: #ffffff;
	}
}

/* 考勤列表 */
.attendance-list {
	padding: 0 30rpx 200rpx;
}

.class-section {
	margin-bottom: 40rpx;
}

.class-header {
	background: #ffffff;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	display: flex;
	align-items: center;
	gap: 20rpx;
	box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
}

.class-icon {
	width: 60rpx;
	height: 60rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
}

.class-info {
	flex: 1;
}

.class-name {
	display: block;
	font-size: 32rpx;
	font-weight: 700;
	color: #333333;
	margin-bottom: 8rpx;
}

.class-count {
	display: block;
	font-size: 24rpx;
	color: #666666;
}

.class-progress {
	width: 120rpx;
	text-align: center;
}

.progress-bar {
	width: 100%;
	height: 8rpx;
	background: #f0f0f0;
	border-radius: 4rpx;
	overflow: hidden;
	margin-bottom: 8rpx;
}

.progress-fill {
	height: 100%;
	background: linear-gradient(90deg, #4caf50 0%, #8bc34a 100%);
	border-radius: 4rpx;
	transition: width 0.3s ease;
}

.progress-text {
	font-size: 20rpx;
	color: #666666;
	font-weight: 600;
}

/* 学生网格 */
.students-grid {
	display: grid;
	grid-template-columns: 1fr;
	gap: 20rpx;
}

.student-card {
	background: #ffffff;
	border-radius: 24rpx;
	padding: 30rpx;
	box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid rgba(255, 255, 255, 0.8);
	transition: all 0.3s ease;

	&:active {
		transform: translateY(-4rpx);
		box-shadow: 0 16rpx 40rpx rgba(0, 0, 0, 0.12);
	}
}

.card-header {
	display: flex;
	align-items: center;
	gap: 20rpx;
	margin-bottom: 24rpx;
}

.student-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	font-weight: 700;

	&.present {
		background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
		color: #4caf50;
	}

	&.absent {
		background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
		color: #f44336;
	}

	&.leave {
		background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
		color: #ff9800;
	}
}

.avatar-text {
	font-size: 28rpx;
	font-weight: 700;
}

.status-indicator {
	position: absolute;
	bottom: 4rpx;
	right: 4rpx;
	width: 20rpx;
	height: 20rpx;
	border-radius: 50%;
	border: 3rpx solid #ffffff;

	&.present { background: #4caf50; }
	&.absent { background: #f44336; }
	&.leave { background: #ff9800; }
}

.student-basic {
	flex: 1;
}

.student-name {
	display: block;
	font-size: 32rpx;
	font-weight: 700;
	color: #333333;
	margin-bottom: 8rpx;
}

.student-no {
	display: block;
	font-size: 24rpx;
	color: #666666;
}

.quick-actions {
	width: 40rpx;
	height: 40rpx;
	border-radius: 50%;
	background: #f8f9fa;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.9);
		background: #e9ecef;
	}
}

.card-content {
	display: flex;
	justify-content: space-between;
	align-items: flex-end;
}

.time-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.time-item {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.time-icon {
	width: 40rpx;
	height: 40rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 20rpx;

	&.checkin {
		background: rgba(76, 175, 80, 0.1);
	}

	&.checkout {
		background: rgba(255, 152, 0, 0.1);
	}

	&.absent {
		background: rgba(244, 67, 54, 0.1);
	}
}

.time-details {
	flex: 1;
}

.time-label {
	display: block;
	font-size: 22rpx;
	color: #666666;
	margin-bottom: 4rpx;
}

.time-value {
	display: block;
	font-size: 26rpx;
	font-weight: 600;
	color: #333333;

	&.absent {
		color: #f44336;
	}
}

.status-badge {
	padding: 12rpx 20rpx;
	border-radius: 50rpx;

	&.present {
		background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
	}

	&.absent {
		background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
	}

	&.leave {
		background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
	}
}

.status-text {
	font-size: 22rpx;
	font-weight: 600;

	.status-badge.present & { color: #4caf50; }
	.status-badge.absent & { color: #f44336; }
	.status-badge.leave & { color: #ff9800; }
}

/* 浮动操作按钮 */
.floating-actions {
	position: fixed;
	bottom: 40rpx;
	left: 50%;
	transform: translateX(-50%);
	width: calc(100% - 60rpx);
	z-index: 100;
}

.fab-container {
	display: flex;
	gap: 20rpx;
}

.fab-btn {
	flex: 1;
	height: 100rpx;
	border-radius: 50rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 16rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	box-shadow: 0 16rpx 40rpx rgba(102, 126, 234, 0.4);
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	position: relative;
	overflow: hidden;

	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: -100%;
		width: 100%;
		height: 100%;
		background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
		transition: left 0.5s;
	}

	&:active {
		transform: translateY(4rpx) scale(0.98);
		box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.6);

		&::before {
			left: 100%;
		}
	}

	&.active {
		background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
		box-shadow: 0 16rpx 40rpx rgba(40, 167, 69, 0.4);

		&:active {
			box-shadow: 0 8rpx 20rpx rgba(40, 167, 69, 0.6);
		}
	}
}

.fab-text {
	font-size: 28rpx;
	font-weight: 600;
	color: #ffffff;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

/* 选择模式样式 */
.student-card {
	&.selection-mode {
		&.selectable {
			border: 2rpx solid rgba(102, 126, 234, 0.3);

			&:active {
				transform: scale(0.98);
			}

			&.selected {
				border-color: #667eea;
				background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
			}
		}

		&:not(.selectable) {
			opacity: 0.6;
			pointer-events: none;
		}
	}
}

.selection-checkbox {
	position: absolute;
	top: -8rpx;
	right: -8rpx;
	width: 32rpx;
	height: 32rpx;
	border-radius: 50%;
	background: #ffffff;
	border: 3rpx solid #e0e0e0;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	&.checked {
		background: #667eea;
		border-color: #667eea;
		transform: scale(1.1);
	}
}

.selection-tip {
	position: fixed;
	top: 200rpx;
	left: 50%;
	transform: translateX(-50%);
	z-index: 99;
}

.tip-content {
	background: rgba(0, 0, 0, 0.8);
	color: #ffffff;
	padding: 20rpx 30rpx;
	border-radius: 50rpx;
	display: flex;
	align-items: center;
	gap: 16rpx;
	backdrop-filter: blur(10rpx);
	animation: tipFadeIn 0.3s ease;
}

.tip-text {
	font-size: 26rpx;
	font-weight: 500;
}

.tip-actions {
	margin-left: 20rpx;
}

.cancel-selection {
	font-size: 24rpx;
	color: #ff6b6b;
	font-weight: 600;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	background: rgba(255, 107, 107, 0.2);
}

@keyframes tipFadeIn {
	from {
		opacity: 0;
		transform: translateX(-50%) translateY(-20rpx);
	}
	to {
		opacity: 1;
		transform: translateX(-50%) translateY(0);
	}
}
</style>
