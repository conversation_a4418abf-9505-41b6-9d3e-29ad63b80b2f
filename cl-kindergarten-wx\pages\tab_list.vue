<template>
	<view class="content">
		<z-paging ref="paging" v-model="dataList" @query="queryList">
			<template #top>
				<z-tabs :list="tabList" @change="tabChange" />
			</template>
		
			
			<!-- 如果希望其他view跟着页面滚动，可以放在z-paging标签内 -->
			<view class="" v-for="(item,index) in dataList" :key="index" @click="itemClick(item)">
				<u-card :title="item.store_name" :sub-title="item.status" >
					<view class="" slot="body">
						<view class="content"><strong>申请：</strong>{{item.master_id}}</view>
						<view class="content"><strong>时间：</strong>{{item.addtime}}</view>
						<view class="content"><strong>数量：</strong>{{item.total_nums}}</view>
					</view>
				</u-card>
			</view>
			
		</z-paging>
	</view>
</template>
<script>
import {toast, clearStorageSync, setStorageSync, getStorageSync, useRouter} from '@/utils/utils.js'
export default {
	data() {
		return {
			dataList: [],
			tabList: ['全部','已审核','待审核'],
			tabIndex: 0,
		}
	},
	onLoad(op) {
		//this.id  = op.id
	},
	onShow() {
	},
	onReady() {
	},
	methods: {
		tabChange(index) {
			this.tabIndex = index;
			this.$refs.paging.reload(true);
		},
		
		queryList(pageNo, pageSize) {
			const params = {
				page: pageNo,
				limit: pageSize,
				type: this.tabIndex + 1
			}
			this.$api.outList(params).then(res => {
				if (res.code == 1) {
					//将请求的结果数组传递给z-paging
					this.$refs.paging.complete(res.data);
				}else{
					this.$refs.paging.complete(false);
				}
			}) 
		},
		itemClick(item) {
			//console.log(item)
			uni.navigateTo({
			    url:'./detail?id='+item.id
			});
		}
	}
}
</script>
<style lang="scss" scoped>	
    page{background:#f2f2f2}
	.content{padding: 20rpx;}
</style>